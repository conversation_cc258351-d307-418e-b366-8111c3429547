@extends('website.layout.master')
@push('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css"/>
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet"/>
    <style>
        input.invalid {
            border: 2px solid red;
        }

        input.valid {
            border: 2px solid green;
        }
    </style>
@endpush

@section('content')
    <div class="container mt-5 pt-5">
        <nav class="mt-5">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="#">Home</a></li>
                <li class="breadcrumb-item active">Order Clubs</li>
            </ol>
        </nav>
    </div>
    <div class="container order-club">
        <form id="order-info" method="POST" action="{{ route('club-order-form-submit') }}">
            @csrf
            <div class="row">
                <div class="col-lg-7 col-12">
                    <h5 class="mb-3 fs-25">Order Information</h5>
                    <div class="card mb-4">
                        <div class="card-body">
                            <h6 class="card-title">Fitter Info</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">First Name</label>
                                    <input type="text" class="form-control" placeholder="John"
                                           value="{{auth()->user()->fitter->FtrNmFirst ?? ''}}" readonly
                                           name="first_name" id="user_first_name">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Last Name</label>
                                    <input type="text" class="form-control" placeholder="Doe" name="last_name"
                                           id="user_last_name" readonly
                                           value="{{auth()->user()->fitter->FtrNmLast ?? ''}}">
                                </div>
                                <div class="col-md-12">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" placeholder="<EMAIL>" readonly
                                           value="{{auth()->user()->email ?? ''}}" name="user_email">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-7 col-12">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">Customer Info</h6>
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Customer Type</label>
                                    <div class="hg-radio-box">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" value="new" id="customerNew"
                                                   name="customer_type"
                                                   @checked(old('customer_type', 'new') == 'new') checked>
                                            <label class="form-check-label" for="customerNew">New</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" value="running"
                                                   id="customerRunning" name="customer_type"
                                                @checked(old('customer_type') == 'running')>
                                            <label class="form-check-label"
                                                   for="customerRunning" @disabled(empty($customers))>Returning </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 gender_section">
                                    <label class="form-label">Gender</label>
                                    <div class="hg-radio-box">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" value="male" id="male"
                                                   name="customer_gender"@checked($formData['customer_gender'] ?? 'male' == 'male')>
                                            <label class="form-check-label" for="male">Male</label>
                                            @error('male')
                                            <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" value="female" id="female"
                                                   name="customer_gender" @checked($formData['customer_gender'] ?? null == 'female')>
                                            <label class="form-check-label" for="female">Female</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row new_form_data g-3">
                                    <div class="col-md-6">
                                        <label class="form-label">First Name</label>
                                        <input type="text" class="form-control"
                                               value="{{ old('first_name', $formData['first_name'] ?? '') }}"
                                               name="first_name" placeholder="">
                                        @error('first_name')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Last Name</label>
                                        <input type="text" class="form-control"
                                               value="{{ old('last_name', $formData['last_name'] ?? '') }}"
                                               name="last_name" placeholder="Smith">
                                        @error('last_name')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-12">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control"
                                               value="{{ old('email', $formData['email'] ?? '') }}" name="email"
                                               placeholder="<EMAIL>">
                                        @error('email')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-12">
                                        <div class="custom_phone">
                                            <label class="form-label">Phone Number</label>
                                            <input type="text" class="form-control contact_number"
                                                   value="{{ old('phone_number', $formData['phone_number'] ?? '') }}"
                                                   name="phone_number" placeholder="(+123) 4568 7890 8520">
                                            <div class="alert-info"></div>
                                            @error('phone_number')
                                            <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <label class="form-label">Street Address</label>
                                        <input type="text" class="form-control"
                                               value="{{ old('street_address', $formData['street_address'] ?? '') }}"
                                               name="street_address" placeholder="315 Nichols Rd #208"
                                               id="address_new_customer">
                                        @error('street_address')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group checkbox_wrapper">
                                            <input type="checkbox" class="same_details" name="save_for_next_time"
                                                   id="same_shipping_address"/>
                                            <label for="same_shipping_address">Same as Street Address</label>
                                            @error('save_for_next_time')
                                            <div class="text-danger">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <label class="form-label">Shipping Address</label>
                                        <input type="text" class="form-control"
                                               value="{{ old('new_shipping_address', $formData['new_shipping_address'] ?? '') }}"
                                               name="new_shipping_address" placeholder="315 Nichols Rd #208"
                                               id="address_line_two_new_customer">
                                        @error('new_shipping_address')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">City</label>
                                        <input type="text" class="form-control"
                                               value="{{ old('city', $formData['city'] ?? '') }}" name="city"
                                               placeholder="Kansas City" id="city_new_customer">
                                        @error('city')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">State / Province / Region</label>
                                        <input type="text" class="form-control"
                                               value="{{ old('state', $formData['state'] ?? '') }}" name="state"
                                               placeholder="Missouri" id="state_new_customer">
                                        @error('state')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">ZIP / Postal Code</label>
                                        <input type="text" class="form-control"
                                               value="{{ old('zip_code', $formData['zip_code'] ?? '') }}"
                                               name="zip_code" placeholder="64112" id="zip_new_customer">
                                        @error('zip_code')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    <div class="col-md-12">
                                        <label class="form-label">Country</label>
                                        <select class="form-select" name="country_id" id="country_new_customer">
                                            <option selected disabled>Select</option>
                                            @foreach($countries as $country)
                                                <option value="{{ $country->CntryCd }}"
                                                        @if(isset($formData['country_id']) && $formData['country_id'] == $country->CntryCd) selected @endif>
                                                    {{ $country->CntryNm ?? '---' }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('country_id')
                                        <div class="text-danger">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="row running_form_data g-3">
                                    <input type="hidden" name="running_first_name" class="running_customer_first_name">
                                    <input type="hidden" name="running_last_name" class="running_customer_last_name">
                                    <div class="col-md-12">
                                        <label class="form-label">Customer</label>
                                        <select class="form-select running_customer_select" name="customer_id" id="">
                                            <option selected disabled>Select Customer</option>
                                            @foreach($customers as $key => $customer)
                                                <option
                                                    value="{{$customer->CustId??''}}">{{$customer->FirstNm??''}} {{$customer->LastNm??''}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control running_customer_email"
                                                   name="running_email" readonly placeholder="Enter Email">
                                        </div>
                                        <div class="col-md-12">
                                            <div class="custom_phone">
                                                <label class="form-label">Phone Number</label>
                                                <input type="text"
                                                       class="form-control running_customer_phone_number running_contact_number"
                                                       readonly name="running_phone_number"
                                                       placeholder="(+123) 4568 7890 8520">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <label class="form-label">Street Address</label>
                                            <input type="text" class="form-control" id="address_running_customer"
                                                   name="running_street_address" placeholder="315 Nichols Rd #208">
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group checkbox_wrapper">
                                                <input type="checkbox" class="same_details"
                                                       name="save_for_next_time_two" id="same_shipping_address_two"/>
                                                <label for="same_shipping_address_two">Same as Street Address</label>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <label class="form-label">Shipping Address</label>
                                            <input type="text" class="form-control" id="customer_address_line_2"
                                                   name="shipping_address" placeholder="315 Nichols Rd #208">
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">City</label>
                                            <input type="text" class="form-control running_customer_city"
                                                   id="city_running_customer" name="running_city"
                                                   placeholder="Kansas City">
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">State / Province / Region</label>
                                            <input type="text" class="form-control running_customer_state"
                                                   id="state_running_customer" name="running_state"
                                                   placeholder="Missouri">
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">ZIP / Postal Code</label>
                                            <input type="text" class="form-control running_customer_zip_code"
                                                   id="post_code_running_customer" name="running_zip_code"
                                                   placeholder="64112">
                                        </div>
                                        <div class="col-md-12">
                                            <label class="form-label">Country</label>
                                            <select class="form-select running_customer_country_id"
                                                    name="running_country_id" id="country_running_customer">
                                                <option selected disabled>Select</option>
                                                @foreach($countries as $country)
                                                    <option
                                                        value="{{$country->CntryCd}}">{{$country->CntryNm ?? '---'}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-5 col-12" id="returning-col" style="opacity:0; display: none">
                    <div class="card">
                        <div class="table-responsive">
                            <table class="datatable table table-row-bordered gy-5">
                                <thead>
                                <tr>
                                    <th>Used</th>
                                    <th>Qty</th>
                                    <th style="min-width:70px;">Line</th>
                                    <th></th>
                                    <th>#</th>
                                    <th>Design</th>
                                    <th>Lie</th>
                                    <th></th>
                                    <th>Loft</th>
                                    <th></th>
                                    <th>Length</th>
                                    <th></th>
                                    <th>Type</th>
                                    <th>Flex</th>
                                    <th>Pure</th>
                                    <th>Face</th>
                                    <th>Hsl</th>
                                    <th>Total$</th>
                                    <th>Club$</th>
                                    <th>Pure$</th>
                                    <th>Action</th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                </tr>
                                </thead>
                                <tbody>
                                @for($i=1; $i<=18; $i++)
                                    <tr>
                                        <td><input type="checkbox" name="used{{$i}}" value="used{{$i}}"></td>
                                        <td>{{$i}}</td>
                                        <td>{{$i}}9.5" Blk</td>
                                        <td>D</td>
                                        <td>{{$i}}</td>
                                        <td>PU-D</td>
                                        <td></td>
                                        <td>{{$i}}7.0</td>
                                        <td></td>
                                        <td>{{$i}}9.5</td>
                                        <td>L{{$i}}.00</td>
                                        <td>4{{$i}}.00</td>
                                        <td>L</td>
                                        <td>W</td>
                                        <td>H</td>
                                        <td>DM</td>
                                        <td></td>
                                        <td>34{{$i}}.00</td>
                                        <td>34{{$i}}.00</td>
                                        <td></td>
                                        <td>N</td>
                                        <td>
                                            <button type="button" name="tbl-{{$i}}" class="action-p">P</button>
                                        </td>
                                        <td>
                                            <button type="button" name="tbl-{{$i}}" class="action-p">P</button>
                                        </td>
                                        <td>
                                            <div class="dropdown">
                                                <button class="btn dropdown-toggle" type="button"
                                                        id="dropdownMenuButton11" data-bs-toggle="dropdown"
                                                        aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton11">
                                                    <li><a class="dropdown-item" href="#">View</a></li>
                                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @endfor
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-12">
                    <h5 class="mb-3 fs-25">Club Details</h5>
                </div>
            </div>

            <div class="club-added">
                <div class="row my-custom-club row-gap-3">
                    <div class="col-lg-7 col-12 order-lg-1 order-2">
                        <div class="card mb-3 club-category">
                            <div class="card-body">
                                <h6 class="card-title">Categories</h6>
                                <div class="row g-3">

                                    <div class="col-md-6">
                                        <label class="form-label">Select Side</label>
                                        <div class="hg-radio-box">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input get-added" type="radio" value="left"
                                                       id="left-side" name="clubs[0][category][side]">
                                                <label class="form-check-label" for="left-side">Left</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input get-added" type="radio" value="right"
                                                       id="right-side" name="clubs[0][category][side]">
                                                <label class="form-check-label" for="right-side">Right</label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">Category</label>
                                        <select class="form-select category_id get-added"
                                                name="clubs[0][category][category_id]">
                                            <option selected disabled>Select</option>
                                            @foreach($categories as $category)
                                                <option
                                                    data-parent-category="{{ $category->parent_category_id ?? null }}"
                                                    value="{{$category->ClbTypeCd}}"
                                                    data-src="{{asset('website')}}/{{$category->image}}">{{$category->ClbTypeDsc}}</option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">Model</label>
                                        <select class="form-select get-added model_id"
                                                name="clubs[0][category][model][model_id]">
                                            <option selected disabled>Select</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Quantity</label>
                                        <input type="number" class="form-control"
                                               name="clubs[0][category][model][quantity]" min="1" placeholder="03">
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Color</label>
                                        <select class="form-select color_id" name="clubs[0][category][model][color]">
                                            <option selected disabled>Select</option>
                                        </select>
                                    </div>
                                    <div class="club-numbers-container"></div>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-3 club-specification specification-card">
                            <div class="card-body">
                                <h6 class="card-title">Specification</h6>
                                <div class="row specs-row g-3">
                                    <div class="col-md-6 lie_div">
                                        <label class="form-label">Lie Angle</label>
                                        <select class="form-select get-added lie_angle_id"
                                                name="clubs[0][category][model][lie_angle_id]">
                                            <option selected disabled>Select</option>
                                            @foreach($lieAngles as $lieAngle)
                                                <option value="{{$lieAngle->LieId??''}}">{{$lieAngle->LieCd??''}}
                                                    - {{$lieAngle->LieDsc??''}} {{$lieAngle->LieDiff??''}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-6 faceangle_div">
                                        <label class="form-label">Face Angle</label>
                                        <select class="form-select get-added faceangle_id"
                                                name="clubs[0][category][model][faceangle_id]">
                                            <option selected>Select</option>
                                            @foreach($faceAngles as $faceAngle)
                                                <option
                                                    value="{{$faceAngle->FaceAngleId??''}}">{{$faceAngle->FaceAngleCd??''}}
                                                    - {{$faceAngle->FaceAngleDsc??''}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-6 loft_div">
                                        <label class="form-label">Loft</label>
                                        <select class="form-select get-added loft_id"
                                                name="clubs[0][category][model][loft_id]">
                                            <option selected>Select</option>
                                            @foreach($lofts as $loft)
                                                <option value="{{$loft->LoftId??''}}">{{$loft->LoftId??''}}
                                                    - {{$loft->LoftDsc??''}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-6 hossel_div">
                                        <label class="form-label">Hossel Positions</label>
                                        <select class="form-select get-added hossel_id"
                                                name="clubs[0][category][model][hossel_id]">
                                            <option selected>Select</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 shaft_len_inc_div">
                                        <label class="form-label">Shaft Len Increment</label>
                                        <div class="hg-radio-box">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input get-added" type="radio" value="1/2 Inch"
                                                       id="1/2 Inch" name="clubs[0][category][shaft_len_increment]">
                                                <label class="form-check-label" for="1/2 Inch">1/2 Inch</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input get-added" type="radio" value="1 Inch"
                                                       id="1 Inch" name="clubs[0][category][shaft_len_increment]">
                                                <label class="form-check-label" for="1 Inch">1 Inch</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">SST Pure</label>
                                        <div class="hg-radio-box">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input get-added" type="radio" value="high"
                                                       id="SST-High" name="clubs[0][category][sst_pure]">
                                                <label class="form-check-label" for="SST-High">SST-High</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input get-added" type="radio" value="low"
                                                       id="SST-Low" name="clubs[0][category][sst_pure]">
                                                <label class="form-check-label" for="SST-Low">SST-Low</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-3 club-shaft">
                            <div class="card-body">
                                <h6 class="card-title">Shaft</h6>
                                <div class="row g-3 shaft-row">
                                    <div class="col-md-12">
                                        <label class="form-label"></label>
                                        <select class="form-select get-added shaft_id"
                                                name="clubs[0][category][shaft][name]">
                                            <option selected disabled>Select</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-3 club-length">
                            <div class="card-body">
                                <h6 class="card-title">Length</h6>
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <label class="form-label">Length</label>
                                        <input type="text" class="form-control" name="clubs[0][lengthData][length]"
                                               oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');">
                                    </div>
                                    <div class="col-md-12">
                                        <label class="form-label">Length Difference (Range)</label>
                                        <select class="form-select range_id" name="clubs[0][lengthData][range]">
                                            <option selected disabled>Select Length Difference (Range)</option>
                                            @foreach($shfLens as $shfLen)
                                                <option value="{{$shfLen->ShfLenCd??''}}"
                                                        data-len-diff="{{$shfLen->ShfLenDiff??''}}">{{$shfLen->ShfLenCd??''}}
                                                    - {{$shfLen->ShfLenDsc??''}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-3 club-grip">
                            <div class="card-body">
                                <h6 class="card-title">Grip</h6>
                                <div class="row g-3 grip-row">
                                    <div class="col-md-6">
                                        <label class="form-label">Size</label>
                                        <select class="form-select" name="clubs[0][grip][size]">
                                            <option selected disabled>Select</option>
                                            @foreach($gripSize as $size)
                                                <option
                                                    value="{{$size->GripSzId}}">{{ $size->GripSzDsc ?? '---' }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Grip Type</label>
                                        <select class="form-select" name="clubs[0][grip][type]">
                                            <option selected disabled>Select</option>
                                            @foreach($gripTypes as $gripType)
                                                <option
                                                    value="{{$gripType->GripTypeId}}">{{ $gripType->GripTypeDsc ?? '---' }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Extra Wraps</label>
                                        <select class="form-select" name="clubs[0][grip][extra-wraps]">
                                            <option selected disabled>Select</option>
                                            <option value="left-1">Left - 1</option>
                                            <option value="left-2">Left - 2</option>
                                            <option value="left-3">Left - 3</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Wrap Side</label>
                                        <select class="form-select" name="clubs[0][grip][wrap-side]">
                                            <option selected disabled>Select</option>
                                            <option value="left">Left</option>
                                            <option value="right">Right</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-5 col-12 order-lg-2 order-1">
                        <div class="card position-sticky" style="top:50px;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <h6 class="card-title">My Club</h6>
                                    <ul class="show-category list-unstyled mb-0 text-end"></ul>
                                </div>
                                <div class="customize-club" data-side="left">
                                    <div class="length position-relative px-5">
                                        <div class="cm"></div>
                                        <div class="grip-size">
                                            <span></span>
                                        </div>
                                        <div class="hotspot top">
                                            <span class="spot"></span>
                                            <div class="hs-box">
                                                <b>Grip</b>
                                                <ul class="mb-0 list-unstyled"></ul>
                                            </div>
                                        </div>
                                        <div class="hotspot middle">
                                            <span class="spot"></span>
                                            <div class="hs-box">
                                                <b>Shaft</b>
                                                <ul class="mb-0 list-unstyled"></ul>
                                            </div>
                                        </div>
                                        <div class="hotspot bottom">
                                            <span class="spot"></span>
                                            <div class="hs-box">
                                                <b>Specifications</b>
                                                <ul class="mb-0 list-unstyled"></ul>
                                            </div>
                                        </div>
                                        <img src="{{asset('website')}}/assets/images/c01.jpg"
                                             class="d-none pe-none img-fluid">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-7 col-12 order-lg-1 order-2">
                        <div class="card club-putter">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div class="d-flex justify-content-between">
                                        <button type="submit" class="btn btn-success me-2">Create</button>
                                        <button type="button" class="btn btn-danger me-2">Cancel</button>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-outline-success btn-add-more">Add More
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    {{--Start Map--}}
    <script>
        function initAutocomplete() {
            var input1 = document.getElementById('address_new_customer');
            var autocomplete1 = new google.maps.places.Autocomplete(input1, {
                componentRestrictions: {country: ['au', 'us']}
            });
            autocomplete1.addListener('place_changed', function () {
                var place = autocomplete1.getPlace();
                var lat = place.geometry.location.lat();
                var lng = place.geometry.location.lng();

                $('#latitude').val(lat);
                $('#longitude').val(lng);

                var city, state, zip, country;
                for (var i = 0; i < place.address_components.length; i++) {
                    var component = place.address_components[i];
                    if (component.types.includes('locality')) {
                        city = component.long_name;
                    }
                    if (component.types.includes('administrative_area_level_1')) {
                        state = component.short_name;
                    }
                    if (component.types.includes('postal_code')) {
                        zip = component.long_name;
                    }
                    if (component.types.includes('country')) {
                        country = component.short_name;
                    }
                }

                $('#city_new_customer').val(city);
                $('#state_new_customer').val(state);
                $('#zip_new_customer').val(zip);
                $('#country_new_customer').val(country);
            });

            var input2 = document.getElementById('address_running_customer');
            var autocomplete2 = new google.maps.places.Autocomplete(input2, {
                componentRestrictions: {country: ['au', 'us']}
            });
            autocomplete2.addListener('place_changed', function () {
                var place = autocomplete2.getPlace();
                var lat = place.geometry.location.lat();
                var lng = place.geometry.location.lng();

                $('#latitude').val(lat);
                $('#longitude').val(lng);

                var city, state, zip, country;
                for (var i = 0; i < place.address_components.length; i++) {
                    var component = place.address_components[i];
                    if (component.types.includes('locality')) {
                        city = component.long_name;
                    }
                    if (component.types.includes('administrative_area_level_1')) {
                        state = component.short_name;
                    }
                    if (component.types.includes('postal_code')) {
                        zip = component.long_name;
                    }
                    if (component.types.includes('country')) {
                        country = component.short_name;
                    }
                }

                $('#city_running_customer').val(city);
                $('#state_running_customer').val(state);
                $('#post_code_running_customer').val(zip);
                $('#country_running_customer').val(country);
            });
        }
    </script>
    {{--End Map--}}

    {{--Start Datatable--}}
    <script>
        $(document).ready(function () {
            $('.datatable').DataTable({
                pagingType: 'simple_numbers',
                lengthChange: false,
                pageLength: 9,
                info: false,
                ordering: false,
                language: {
                    search: '',
                    searchPlaceholder: 'Search',
                    paginate: {
                        previous: '<i class="fa-solid fa-chevron-left"></i>',
                        next: '<i class="fa-solid fa-chevron-right"></i>'
                    }
                },
                dom: '<"data-search-field"f>rt<"bottom"p>',
                initComplete: function () {
                    $('.dataTables_filter').addClass('text-start');
                }
            });
        });
    </script>
    {{--End Datatable--}}

    {{--Start Phone Number --}}
    <script>
        const phoneInputField = document.querySelector(".contact_number");
        const phoneInput = window.intlTelInput(phoneInputField, {
            utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js",
            nationalMode: true, // full international number
            formatOnDisplay: true, // format while typing
            autoPlaceholder: "polite", // show placeholder
            separateDialCode: false,
        });
        const info = document.querySelector(".alert-info");

        function process(event) {
            event.preventDefault();
            const isValid = phoneInput.isValidNumber();
            const phoneNumber = phoneInput.getNumber();
            info.style.display = "";
            if (isValid) {
                info.innerHTML = `✅ Valid phone number: <strong>${phoneNumber}</strong>`;
            } else {
                info.innerHTML = `❌ Invalid phone number. Please enter a valid one.`;
            }
        }

        phoneInputField.addEventListener("keypress", function (e) {
            const char = String.fromCharCode(e.which);
            if (!/[0-9+\-\s]/.test(char)) {
                e.preventDefault();
            }
        });
        phoneInputField.addEventListener("blur", function () {
            if (phoneInput.isValidNumber()) {
                phoneInputField.classList.add("valid");
                phoneInputField.classList.remove("invalid");
            } else {
                phoneInputField.classList.add("invalid");
                phoneInputField.classList.remove("valid");
            }
        });
    </script>
    {{--End Phone Number --}}

    {{--Start Auto Save Data--}}
    <script>
        let controller = null;
        let timeout = null;
        document.getElementById('order-info').addEventListener('keyup', handleFormChange);
        document.getElementById('order-info').addEventListener('change', handleFormChange);

        function handleFormChange() {

            if (timeout) {
                clearTimeout(timeout);
            }
            if (controller) {
                controller.abort();
            }
            controller = new AbortController();
            const signal = controller.signal;
            const formData = new FormData(document.getElementById('order-info'));
            const formDataObj = {};
            formData.forEach(function (value, key) {
                formDataObj[key] = value;
            });
            timeout = setTimeout(function () {
                fetch("{{ route('order-save-draft') }}", {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formDataObj),
                    signal: signal
                }).then(response => {
                    return response.json();
                }).then(data => {
                    console.log('Draft saved:', data);
                }).catch(error => {
                    if (error.name === 'AbortError') {
                        console.log('Previous request aborted');
                    } else {
                        console.log('Error saving draft:', error);
                    }
                });
            }, 500);
        }
    </script>
    {{--End Auto Save Data--}}

    <script>
        $(document).ready(function ($) {
            $('#order-info [name="customer_type"]').on('change', function () {
                if ($(this).val() === 'running' && $(this).is(':checked')) {
                    $('#returning-col').show();
                    $('#returning-col').css('opacity', '1');
                } else {
                    $('#returning-col').hide();
                }
            });
            $(document).on('change', '.category_id', function () {
                var categoryId = $(this).val();
                var parentCategory = $(this).find('option:selected').data('parent-category');

                var modelSelect = $(this).closest('.row').find('.model_id');
                var colorSelect = $(this).closest('.row').find('.color_id');
                var sideRadios = $(this).closest('.row').find('.get-added[name="side[]"]');
                var modelId = modelSelect.data('model-id');

                modelSelect.html('<option disabled selected>Select</option>');
                colorSelect.html('<option disabled selected>Select</option>').prop('disabled', true);
                sideRadios.prop('disabled', false);

                var handSide = $(this).closest('.row').find('input[name^="clubs"][name$="[category][side]"]:checked').val();

                if (categoryId) {
                    $.ajax({
                        url: '{{ url('get-models') }}/' + categoryId + '?side=' + handSide,
                        type: 'GET',
                        dataType: 'json',
                        success: function (data) {
                            $.each(data, function (index, model) {
                                var isSelected = model.HeadDsgnId == modelId ? 'selected' : '';
                                modelSelect.append('<option value="' + model.HeadDsgnId + '" ' + isSelected + '>' + model.HeadDsgnDsc + '</option>');
                            });
                            modelSelect.trigger('change');
                            $('.lie_div').show();
                            console.log("parentCategory", parentCategory);
                            if (parentCategory === 'W') {
                                $('.faceangle_div').show();
                                $('.shaft_len_inc_div').show();
                            } else {
                                $('.faceangle_div').hide();
                                $('.shaft_len_inc_div').hide();
                            }

                            if (parentCategory === 'I') {
                                $('.loft_div').show();
                            } else {
                                $('.loft_div').hide();
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Error fetching models:', error);
                        }
                    });
                }
            });
            $(document).on('change', '.category_id', function () {
                var categoryId = $(this).val();
                var row = $(this).closest('.club-category').siblings('.club-shaft');
                var shaftSelect = row.find('.shaft-row .shaft_id');
                shaftSelect.html('<option selected disabled>Select</option>').prop('disabled', true);
                var shaftId = shaftSelect.data('shaft-id');
                if (categoryId) {
                    $.ajax({
                        url: '{{ url('get-shafts') }}/' + categoryId,
                        type: 'GET',
                        dataType: 'json',
                        success: function (data) {
                            if (data.length > 0) {
                                shaftSelect.prop('disabled', false);
                                $.each(data, function (index, shaft) {
                                    var isSelected = shaft.id == shaftId ? 'selected' : '';
                                    shaftSelect.append('<option value="' + shaft.id + '" ' + isSelected + '>' + shaft.name + '</option>');
                                });
                                shaftSelect.trigger('change');
                            } else {
                                shaftSelect.html('<option selected disabled>No shafts available</option>');
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Error fetching shafts:', error);
                        }
                    });
                }
            });
            $(document).on('change', '.model_id', function () {
                var modelId = $(this).val();
                var colorSelect = $(this).closest('.row').find('.color_id');
                var colorData = colorSelect.data('color-id');
                var quantityInput = $(this).closest('.row').find('input[name$="[quantity]"]');
                var clubNumbersContainer = $(this).closest('.row').find('.club-numbers-container');
                var parentRow = $(this).closest('.club-added');
                var hosselSelect = parentRow.find('.hossel_id');
                hosselSelect.empty();
                hosselSelect.append('<option selected>Select Hossel</option>');

                colorSelect.html('<option selected disabled>Select</option>').prop('disabled', true);
                clubNumbersContainer.html('');

                quantityInput.prop('readonly', false);

                if (modelId) {
                    $.ajax({
                        url: '{{ url('get-colors') }}/' + modelId,
                        type: 'GET',
                        dataType: 'json',
                        success: function (data) {
                            if (data.color_options.length > 0) {
                                colorSelect.prop('disabled', false);
                                $.each(data.color_options, function (index, color) {
                                    var isSelected = color.name == colorData ? 'selected' : '';
                                    colorSelect.append('<option value="' + color.id + '"  ' + isSelected + '  style="background-color:' + color.name + '; color: #fff;">' + color.name + '</option>');
                                });
                                colorSelect.trigger('change');
                                $.each(['loft', 'lie', 'hossel', 'faceangle'], function (_, flag) {
                                    const flagKey = `has_${flag}`;
                                    const divClass = `.${flag}_div`;
                                    $(divClass).toggle(data.model[flagKey] == 1);
                                });
                                var clubNumbers = JSON.parse(data.model.club_numbers);
                                if (clubNumbers) {
                                    var clubNumbersHtml = '<div class="col-md-12 club-number-checkboxes-wrapper"><label class="form-label">Club Numbers</label><div class="club-number-checkboxes">';
                                    $.each(clubNumbers, function (index, club_number) {
                                        clubNumbersHtml += `
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input club-number-radio" type="checkbox" name="clubs[0][category][model][club_numbers][]" value="${club_number}">
                                                    <label class="form-check-label">${club_number}</label>
                                                </div>
                                            `;
                                    });
                                    clubNumbersHtml += '</div></div>';
                                    clubNumbersContainer.html(clubNumbersHtml);
                                    quantityInput.prop('readonly', true);
                                }
                                var hossels = (data.model.hossels);
                                if (hossels && hossels.length > 0) {
                                    hossels.forEach(function (hossel) {
                                        hosselSelect.append(
                                            '<option value="' + hossel.id + '">' + hossel.name + '</option>'
                                        );
                                    });
                                }
                            } else {
                                colorSelect.html('<option selected disabled>No colors available</option>');
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Error fetching colors:', error);
                        }
                    });
                }
                $(document).on('change', '.club-number-radio', function () {
                    var selectedClubNumber = $(this).val();

                    if (selectedClubNumber) {
                        quantityInput.val(selectedClubNumber);
                    } else {
                        quantityInput.val('');
                    }
                });

            });
            $('.range_id').on('change', function () {
                var lenDiff = $(this).find('option:selected').data('len-diff');
                $(this).closest('.club-length').find('input[name$="[lengthData][length]"]').val(lenDiff);
            });
            $(document).on('change', '.shaft_id', function () {
                var shaftId = $(this).val();
                var row = $(this).closest('.shaft-row');
                var parentForm = $(this).closest('form');
                var completedClub = $('.club-section').length;
                // Remove old selects/hidden fields

                row.find('.material-wrapper, .torque-wrapper, .weight-wrapper, .flex-wrapper, .length-wrapper, input.single-value-field , .club-number-checkboxes-wrapper, .sst-pure-wrapper').remove('');

                if (!shaftId) return;

                $.ajax({
                    url: '{{ url("get-shaft-details") }}/' + shaftId,
                    type: 'GET',
                    dataType: 'json',
                    success: function (response) {
                        if (!response.success) {
                            console.error('Error fetching shaft details:', response.message);
                            return;
                        }

                        var data = response.data;

                        // Reusable function to create select or hidden input
                        function appendField(wrapperClass, label, selectClass, fieldName, options, selectedValue) {
                            if (Array.isArray(options) && options.length > 1) {
                                let html = `
                                        <div class="col-md-6 ${wrapperClass}">
                                            <label class="form-label">${label}</label>
                                            <select class="form-select ${selectClass}" name="${fieldName}">
                                                <option selected disabled>Select ${label}</option>
                                                ${options.map(opt => `
                                                    <option value="${opt.id}" ${opt.name === selectedValue ? 'selected' : ''}>${opt.name}</option>
                                                `).join('')}
                                            </select>
                                        </div>
                                    `;
                                row.append(html);
                            } else if (options.length === 1) {
                                row.append(`
                                        <input type="hidden" class="single-value-field" name="${fieldName}" value="${options[0].id}">
                                    `);
                            }
                        }

                        // MATERIAL
                        appendField(
                            'material-wrapper',
                            'Material',
                            'material_id',
                            `clubs[${completedClub}][category][shaft][material]`,
                            data.materials || [],
                            row.find('.material_id').data('material-id')
                        );

                        // WEIGHT
                        appendField(
                            'weight-wrapper',
                            'Weight',
                            'weight_id',
                            `clubs[${completedClub}][category][shaft][weight]`,
                            data.weights || [],
                            row.find('.weight_id').data('weight-id')
                        );

                        // FLEX
                        appendField(
                            'flex-wrapper',
                            'Flex',
                            'flex_id',
                            `clubs[${completedClub}][category][shaft][flex]`,
                            data.flexes || [],
                            row.find('.flex_id').data('flex-id')
                        );
                    },
                    error: function (xhr, status, error) {
                        console.error('Error fetching shaft details:', error);
                    }
                });
            });
        });
        $(document).ready(function () {
            $('input[name="customer_type"]').on('change', function () {
                if ($(this).val() === 'new') {
                    $('.new_form_data, .gender_section').addClass('show');
                    $('.running_form_data').removeClass('show');
                } else if ($(this).val() === 'running') {
                    $('.running_form_data').addClass('show');
                    $('.new_form_data, .gender_section').removeClass('show');
                }
            });
            $('input[name="customer_type"]:checked').trigger('change');

            $(document).on('change', '.running_customer_select', function () {
                var customerId = $(this).val();
                $.ajax({
                    url: '{{ url('get-running-customer-details') }}/' + customerId,
                    type: 'GET',
                    dataType: 'json',
                    success: function (data) {
                        ;
                        $('.running_customer_first_name').val(data.customer.FirstNm);
                        $('.running_customer_last_name').val(data.customer.LastNm);
                        $('.running_customer_email').val(data.customer.EMailAddr);
                        $('.running_customer_phone_number').val(data.customer.PhnNum);
                        $('.running_street_address').val(data.customer.Addr);
                        $('.running_customer_city').val(data.customer.CityNm);
                        $('.running_customer_state').val(data.customer.StateCd);
                        $('.running_customer_zip_code').val(data.customer.PostalCd);
                        $('.running_customer_country_id').val(data.customer.CntryCd);
                    },
                    error: function (xhr, status, error) {
                        console.error('Error fetching shaft details:', error);
                    }
                });
            });
        });
    </script>

    <script>
        $(function () {
            function handleAddressCopy(checkboxId, sourceId, targetId) {
                $(checkboxId).change(function () {
                    $(targetId).val(this.checked ? $(sourceId).val() : $(targetId).val());
                }).change();
            }

            handleAddressCopy('#same_shipping_address', '#address_new_customer', '#address_line_two_new_customer');
            handleAddressCopy('#same_shipping_address_two', '#address_running_customer', '#customer_address_line_2');
        });

        $(document).ready(function ($) {
            $(document).on('click', '.hotspot .spot', function () {
                $(this).closest('.hotspot').find('.hs-box').toggleClass('show-hs');
            });

            $(document).ajaxComplete(function () {
                StickyClub();
            });

            function StickyClub() {
                let _club = $('.customize-club');
                let myClubParent = _club.closest('.my-custom-club');
                let categorySide = myClubParent.find('[name$="[category][side]"]:checked').val();
                let quantityField = myClubParent.find('[name$="[category][model][quantity]"]');
                let name = quantityField.closest('div').find('input').attr('name');
                let quantity = quantityField.val();
                quantity = quantity === 0 ? 'No Club' : quantity < 2 ? quantity + ' Club' : quantity + ' Clubs';

                myClubParent.find(_club).attr('data-side', categorySide);

                let modelContainer = myClubParent.find('.show-category [data-name$="[category][model][model_id]"]');
                let lastKey = name.split('[').pop().replace(']', '');
                let existingItem = myClubParent.find('li[data-name$="[' + lastKey + ']"]');

                if (existingItem.length === 0) {
                    modelContainer.after('<li data-name="' + name + '">' + quantity + '</li>');
                } else {
                    existingItem.text(quantity);
                }

                /*$('.hotspot .hs-box ul li').each(function(){
                    if($(this).text() == ""){
                        $(this).closest('.hs-box').removeClass('show-hs');
                        $(this).closest('.hotspot').find('.spot').removeAttr('style');
                    }
                });*/
            }

            /*$('.customize-club').each(function (index , club) {*/
            $(document).on('change keyup', '.club-shaft select, .club-specification select, .club-specification input,  .club-grip select, .club-category select, .club-category input, .club-length select', function () {
                // let isShaft = $(this).closest('.club-shaft');
                let isShaft = $(this).closest('.club-shaft').length > 0;
                let isSpecification = $(this).closest('.club-specification').length > 0;
                let isGrip = $(this).closest('.club-grip').length > 0;
                let isCategory = $(this).closest('.club-category').length > 0;
                let isLength = $(this).closest('.club-length').length > 0;

                let myCustomClub = $(this).closest('.my-custom-club');
                let club = myCustomClub.find('.customize-club');
                console.log('isSpecification', isSpecification);
                let rowClass = isShaft ? myCustomClub.find('.shaft-row > div') : isSpecification ? myCustomClub.find('.specs-row > div') : isGrip ? myCustomClub.find('.grip-row > div') : null;
                // Store the jQuery objects, not concatenate them as selectors
                let hotspotElement = isShaft ? myCustomClub.find('.hotspot:is(.middle)') : isSpecification ? myCustomClub.find('.hotspot:is(.bottom)') : isGrip ? myCustomClub.find('.hotspot:is(.top)') : myCustomClub.find('.show-category');

                console.log('rowClass: ' + rowClass)
                console.log('hotspotElement: ' + hotspotElement)

                let shaftRow = rowClass || null;
                let name = $(this).attr('name');
                let value = $(this).val();
                let text = $(this).is('select') ? $(this).find(':selected').text() : value;

                if (text === "Select") {
                    text = '';
                }

                if (text !== "Select" && text !== '') {
                    $(this).find('option').removeAttr('selected');
                    $(this).find('option:selected').attr('selected', 'selected');
                }

                if (name.includes("[category][side]")) {
                    const $radio = $(this);
                    const wasChecked = $radio.attr('checked') === 'checked';
                    $(`[name="${$radio.attr('name')}"]`).removeAttr('checked');

                    if (!wasChecked) {
                        $radio.attr('checked', 'checked');
                    }
                }

                if (name.includes("[category][model][quantity]")) {
                    $(this).attr('value', value);
                }

                // Remove disabled attribute (unchanged)
                if ($(this).closest('[class*=col-md]').next().length > 0) {
                    $(this).closest('[class*=col-md]').next().find('select, input').prop('disabled', false);
                } else {
                    $(this).closest('.card').next().find('.row > :first-child select, .row > :first-child input').prop('disabled', false);
                }

                // Reset logic for Shaft, Putter, and Grip (updated to ignore index)
                if ((isShaft && name.includes("[shaft][name]")) ||
                    (isSpecification && name.includes("[category][model][lie_angle_id]")) ||
                    (isGrip && (name.includes("[grip][size]")))) {
                    if (text === "") {
                        $(shaftRow).each(function () {
                            let $select = $(this).find('select');
                            if ($select.prop('selectedIndex') !== 0) {
                                $select.prop('selectedIndex', 0).trigger('change');
                            }
                        });

                        // Use find instead of string concatenation
                        console.log("hotspotElement 1", hotspotElement);
                        hotspotElement.find('.spot').css({'transition': 'opacity 0.5s', 'opacity': '0'});
                        hotspotElement.find('.hs-box').removeClass('show-hs');

                        if (isGrip) {
                            $(myCustomClub).find('.grip-size span').text('');
                            $(myCustomClub).find('.grip-size').removeClass('show');
                        }
                    } else {
                        // Use find instead of string concatenation
                        console.log("hotspotElement", hotspotElement);
                        hotspotElement.find('.spot').css({'transition': 'opacity 0.5s', 'opacity': '1'});

                        if (isGrip) {
                            $(myCustomClub).find('.grip-size').addClass('show');
                            if (isGrip && name.includes("[grip][size]")) {
                                $(myCustomClub).find('.grip-size span').text(text);
                            }
                        }
                    }

                    // return;
                }

                // Special logic for category fields (updated to ignore index)
                if (isCategory || isLength) {
                    if (name.includes("[quantity]") && !isNaN(value)) {
                        value = parseInt(value);
                        text = value === 0 ? 'No Club' : value < 2 ? value + ' Club' : value + ' Clubs';
                    }

                    if (name.includes("[category][side]")) {
                        let side = $(this).val();
                        $(club).attr('data-side', side);
                    }
                    if (name.includes("[category][category_id]")) {
                        let img_src = $(this).find(':selected').data('src');
                        let img_tag = $(club).find('img');
                        $(img_tag).attr('src', img_src);
                        $(img_tag).removeClass('d-none');
                    }

                    if (name.includes("[lengthData][range]")) {
                        if (text === "Select" || text === "") {
                            text = '';
                            $(myCustomClub).find('.length .cm').text('');
                            $(myCustomClub).find('.length').removeClass('show-brdr');
                        } else {
                            $(myCustomClub).find('.length .cm').text(text);
                            $(myCustomClub).find('.length').addClass('show-brdr');
                        }
                    }

                    let lastKey = name.split('[').pop().replace(']', '');
                    let listItem = $(myCustomClub).find('.show-category').find('li[data-name$="[' + lastKey + ']"]');
                    if (listItem.length > 0) {
                        listItem.text(text);
                    } else {
                        if (name.includes("[category][side]") || name.includes("[category][model][color]") || name.includes("[lengthData][range]") || name.includes("[lengthData][length]")) {
                            return;
                        } else {
                            $(myCustomClub).find('.show-category').append('<li data-name="' + name + '">' + text + '</li>');
                        }
                    }

                    return;
                }

                // General list update for Shaft, Putter, and Grip (modified to use find)
                let lastKey = name.split('[').pop().replace(']', '');
                let listItem = hotspotElement.find('ul').find('li[data-name$="[' + lastKey + ']"]');
                if (listItem.length > 0) {
                    listItem.text(text);
                } else {
                    hotspotElement.find('ul').append('<li data-name="' + name + '">' + text + '</li>');
                }
                // Show hs-box when a valid selection is made (modified to use find)
                if (shaftRow) {
                    $(shaftRow).each(function () {
                        let selected = $(this).find('select :selected');
                        if (selected.length && selected.text() !== 'Select') {
                            hotspotElement.find('.hs-box').addClass('show-hs');
                        }
                    });
                }

            });

            /*});*/

            function getLastNamePart(fullName) {
                const parts = fullName.split('][');
                const lastPart = parts[parts.length - 1];
                if (fullName.includes('_')) {
                    if (parts.length >= 2) {
                        const secondLastPart = parts[parts.length - 2];
                        return secondLastPart.replace(/[\[\]]/g, '');
                    }
                }
                return lastPart.replace(/[\[\]]/g, '');
            }

            $(document).on('click', '.btn-add-more', function () {
                let clubDetail = $('.club-added');
                let count = clubDetail.find('.club-section').length;
                count = count === 0 ? 1 : count + 1;

                let customizeClub = $('.my-custom-club .customize-club').closest('.card-body').html();

                let customizeClubClone = $(this).closest('.row').siblings('.my-custom-club').clone(true, true);

                customizeClubClone.find('.col-lg-5, .card.club-putter').remove();
                customizeClubClone.find('.col-lg-7').removeClass('col-lg-7').addClass('col-lg-12');

                // Clear values in the cloned form before adding it
                customizeClubClone.find('[name^="clubs["]').each(function () {
                    $(this).attr('name', function (_, oldName) {
                        return oldName.replace(/clubs\[\d+\]/g, `clubs[${count}]`);
                    });

                    $(this).val('');

                    if ($(this).is('select')) {
                        $(this).prop('selectedIndex', 0);
                    }

                    if ($(this).is(':checkbox, :radio')) {
                        $(this).prop('checked', false);
                    }
                });

                // Update IDs for radio buttons in cloned form
                customizeClubClone.find('[name$="[category][side]"]').each(function () {
                    let $this = $(this);
                    let _id = $this.attr('id');
                    let _label = $this.next('label');
                    $this.attr('id', _id + count);
                    _label.attr('for', _id + count);
                });

                // Update IDs for other radio buttons in cloned form
                customizeClubClone.find('input[type="radio"]').each(function () {
                    let $this = $(this);
                    let _id = $this.attr('id');
                    let _label = $this.next('label');
                    if (_id && !_id.includes(count)) {
                        $this.attr('id', _id + count);
                        _label.attr('for', _id + count);
                    }
                });

                $(clubDetail).find('.my-custom-club').last().before(`
                        <div class="row club-section">
                            <div class="col-md-7">
                                <div class="mb-3 club-detail">
                                    <div class="accordion" id="accordionClub` + count + `">
                                        <div class="accordion-item">
                                            <h6 class="card-title accordion-header" id="headingClub` + count + `">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseClub` + count + `" aria-expanded="false" aria-controls="collapseClub` + count + `">
                                                    Club ` + count + `
                                                    <i class="fa-solid fa-chevron-down"></i>
                                                </button>
                                            </h6>
                                            <div id="collapseClub` + count + `" class="accordion-collapse collapse" aria-labelledby="headingClub` + count + `" data-bs-parent="#accordionClub` + count + `" style="">
                                                <div class="accordion-body">
                                                      ${customizeClubClone.html()}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-5">
                                 <div class="card mb-5" style="zoom: .4; max-width: fit-content;">
                                    <div class="card-body my-club">
                                         ` + customizeClub + `
                                    </div>
                                 </div>
                            </div>
                        </div>
                    `);

            });

            $(document).on('change', 'input[name^="clubs"][name$="[category][side]"]', function () {
                var categorySelect = $(this).closest('.row').find('.category_id');
                categorySelect.trigger('change');
            });
        });
        $(document).ready(function () {
            function triggerChangeIfNotSelect(selector) {
                let element = $(selector);
                let value = element.find('option:selected').val();
                if (value !== 'Select') {
                    element.trigger('change');
                }
            }

            triggerChangeIfNotSelect('.category_id');
            triggerChangeIfNotSelect('.size-select');
            triggerChangeIfNotSelect('.grip-select');
            triggerChangeIfNotSelect('.extra-select');
            triggerChangeIfNotSelect('.wrap-select');
        });
    </script>

@endpush
